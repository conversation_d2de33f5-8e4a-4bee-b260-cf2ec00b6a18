<template>
  <lumi-sidenav
    icon="mdi-test-tube"
    class="fixed-end lumi-sidenav"
    v-if="showSidenav"
    :config="sidenavConfig"
    @action="handleSidenavAction"
  ></lumi-sidenav>

  <div class="main-page-content">
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5>Teste de Layout com Scroll</h5>
              <p class="text-muted mb-0">Este é um teste para verificar se o scroll funciona corretamente com a LumiSidenav</p>
            </div>
            <div class="card-body">
              <h6>Instruções de Teste:</h6>
              <ol>
                <li>Esta página deve ter conteúdo suficiente para gerar scroll vertical</li>
                <li>O scroll deve aparecer dentro do container de conteúdo, não atrás da sidenav</li>
                <li>A sidenav deve permanecer fixa à direita</li>
                <li>O conteúdo não deve ser sobreposto pela sidenav</li>
              </ol>

              <!-- Conteúdo longo para forçar scroll -->
              <div v-for="i in 50" :key="i" class="mb-3">
                <div class="card">
                  <div class="card-body">
                    <h6 class="card-title">Item {{ i }}</h6>
                    <p class="card-text">
                      Este é o item número {{ i }} criado para testar o comportamento do scroll.
                      O conteúdo deve ser visível e o scroll deve funcionar corretamente sem
                      interferência da sidenav fixa. Lorem ipsum dolor sit amet, consectetur
                      adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna
                      aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
                    </p>
                    <div class="row">
                      <div class="col-md-6">
                        <small class="text-muted">Timestamp: {{ new Date().toLocaleString() }}</small>
                      </div>
                      <div class="col-md-6 text-end">
                        <button class="btn btn-sm btn-outline-primary" @click="scrollToTop">
                          Voltar ao Topo
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="alert alert-success mt-4">
                <h6>✅ Teste Concluído</h6>
                <p class="mb-0">
                  Se você consegue ver esta mensagem e o scroll funcionou corretamente,
                  a implementação está funcionando como esperado!
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";

export default {
  name: "TestScrollLayout",
  components: {
    LumiSidenav,
  },
  data() {
    return {
      sidenavConfig: {
        groups: [
          {
            title: "TESTE",
            buttons: [
              {
                text: "Scroll para o Topo",
                icon: "keyboard_arrow_up",
                iconType: "material",
                action: "scrollToTop"
              },
              {
                text: "Scroll para o Meio",
                icon: "keyboard_arrow_down",
                iconType: "material",
                action: "scrollToMiddle"
              },
              {
                text: "Scroll para o Final",
                icon: "keyboard_double_arrow_down",
                iconType: "material",
                action: "scrollToBottom"
              }
            ]
          },
          {
            title: "AÇÕES",
            buttons: [
              {
                text: "Adicionar Item",
                icon: "add",
                iconType: "material",
                action: "addItem"
              },
              {
                text: "Limpar Lista",
                icon: "clear_all",
                iconType: "material",
                action: "clearItems"
              }
            ]
          }
        ]
      }
    };
  },
  computed: {
    ...mapState(["showSidenav"]),
  },
  methods: {
    ...mapMutations(["navbarMinimize"]),

    handleSidenavAction(action, button) {
      console.log(`Action: ${action}`, button);

      switch (action) {
        case 'scrollToTop':
          this.scrollToTop();
          break;
        case 'scrollToMiddle':
          this.scrollToMiddle();
          break;
        case 'scrollToBottom':
          this.scrollToBottom();
          break;
        case 'addItem':
          this.addItem();
          break;
        case 'clearItems':
          this.clearItems();
          break;
      }
    },

    scrollToTop() {
      const scrollContainer = document.querySelector('.main-content-scrollable');
      if (scrollContainer) {
        scrollContainer.scrollTo({ top: 0, behavior: 'smooth' });
      }
    },

    scrollToMiddle() {
      const scrollContainer = document.querySelector('.main-content-scrollable');
      if (scrollContainer) {
        const middle = scrollContainer.scrollHeight / 2;
        scrollContainer.scrollTo({ top: middle, behavior: 'smooth' });
      }
    },

    scrollToBottom() {
      const scrollContainer = document.querySelector('.main-content-scrollable');
      if (scrollContainer) {
        scrollContainer.scrollTo({ top: scrollContainer.scrollHeight, behavior: 'smooth' });
      }
    },

    addItem() {
      // Simular adição de item (apenas para teste)
      console.log('Item adicionado (simulação)');
    },

    clearItems() {
      // Simular limpeza de itens (apenas para teste)
      console.log('Itens limpos (simulação)');
    }
  },

  mounted() {
    console.log('TestScrollLayout montado - verificando layout...');
    
    // Verificar se a estrutura está correta
    setTimeout(() => {
      const mainContent = document.querySelector('.main-content');
      const scrollableContent = document.querySelector('.main-content-scrollable');
      const sidenav = document.querySelector('.lumi-sidenav');
      
      console.log('Main content:', mainContent);
      console.log('Scrollable content:', scrollableContent);
      console.log('Sidenav:', sidenav);
      
      if (scrollableContent) {
        console.log('Scrollable height:', scrollableContent.scrollHeight);
        console.log('Scrollable client height:', scrollableContent.clientHeight);
      }
    }, 1000);
  }
};
</script>

<style scoped>
.card {
  transition: transform 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
}

.alert {
  border-left: 4px solid #28a745;
}
</style>
